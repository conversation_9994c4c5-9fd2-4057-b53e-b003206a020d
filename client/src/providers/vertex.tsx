import React, { useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const VertexProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [projectId, setProjectId] = useState('');
  const [region, setRegion] = useState('us-central1');
  // Vertex exposes models via gcloud CLI; we list the common ones
  const models = [
    { id: 'claude-3-5-sonnet@20241022', name: 'Claude 3.5 Sonnet' },
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <input
        type="text"
        placeholder="GCP Project ID"
        value={projectId}
        onChange={(e) => setProjectId(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        value={region}
        onChange={(e) => setRegion(e.target.value)}
      >
        <option value="us-central1">us-central1</option>
        <option value="europe-west1">europe-west1</option>
        <option value="asia-southeast1">asia-southeast1</option>
      </select>
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('vertex', projectId, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('vertex', projectId); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};