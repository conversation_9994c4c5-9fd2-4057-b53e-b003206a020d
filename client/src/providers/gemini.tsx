import React, { useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const GeminiProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const models = [
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <input
        type="password"
        placeholder="AIza..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('gemini', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('gemini', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};