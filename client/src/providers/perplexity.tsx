import React, { useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const PerplexityProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const models = [
    { id: 'llama-3-sonar-large-32k-online', name: 'Sonar Large Online' },
    { id: 'llama-3-sonar-small-32k-online', name: 'Sonar Small Online' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <input
        type="password"
        placeholder="pplx-..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('perplexity', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('perplexity', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};