import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const AnthropicProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [models] = useState([
    { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet' },
    { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus' },
    { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku' },
  ]);

  return (
    <div className="space-y-3 text-sm">
      <input
        type="password"
        placeholder="sk-ant-..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('anthropic', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('anthropic', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};