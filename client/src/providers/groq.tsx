import React, { useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';

export const GroqProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const models = [
    { id: 'llama3-70b-8192', name: 'LLaMA 3 70b' },
    { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7b' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <input
        type="password"
        placeholder="gsk_..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('groq', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('groq', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};